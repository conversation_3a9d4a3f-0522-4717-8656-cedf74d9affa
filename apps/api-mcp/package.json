{"author": "", "bin": {"weather": "./build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.18.2", "zod": "3"}, "description": "", "devDependencies": {"@types/node": "^24.6.1", "typescript": "^5.9.3"}, "files": ["build"], "keywords": [], "license": "ISC", "main": "index.js", "name": "api-mcp", "scripts": {"build": "tsc && chmod 755 build/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "version": "1.0.0"}