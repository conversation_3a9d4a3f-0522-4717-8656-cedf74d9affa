{
    "extends": "../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": "./src",
        "outDir": "./build",
        "target": "ES2022",
        "lib": ["es2021"],
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        // Set `sourceRoot` to  "/" to strip the build path prefix
        // from generated source code references.
        // This improves issue grouping in Sentry.
        "sourceRoot": "/"
    },
    "references": [
        {
            "path": "../../packages/malou-package-models"
        },
        {
            "path": "../../packages/malou-utils"
        },
        {
            "path": "../../packages/malou-dto"
        },
        {
            "path": "../../packages/malou-emails"
        },
        {
            "path": "../../packages/crawlers"
        }
    ],
    "include": ["src/**/*"]
}
